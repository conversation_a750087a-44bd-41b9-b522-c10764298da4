import { supabase } from '../lib/supabase';

class InvestmentEarningsService {
  constructor() {
    this.isProcessing = false;
  }

  // Calculate daily earnings for all active investments
  async processAllDailyEarnings() {
    if (this.isProcessing) {
      console.log('Daily earnings processing already in progress');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('Starting daily earnings processing...');

      // Get all active investments
      const { data: investments, error } = await supabase
        .from('investments')
        .select(`
          *,
          investment_plans (
            name,
            daily_return,
            total_return,
            duration_days
          ),
          users (
            auth_id,
            balance
          )
        `)
        .eq('status', 'active');

      if (error) throw error;

      const today = new Date().toISOString().split('T')[0];
      const processedInvestments = [];

      for (const investment of investments) {
        try {
          await this.processSingleInvestmentEarning(investment, today);
          processedInvestments.push(investment.id);
        } catch (error) {
          console.error(`Error processing investment ${investment.id}:`, error);
        }
      }

      console.log(`Processed ${processedInvestments.length} investments for ${today}`);
      return processedInvestments;

    } catch (error) {
      console.error('Error in processAllDailyEarnings:', error);
      throw error;
    } finally {
      this.isProcessing = false;
    }
  }

  // Process earnings for a single investment
  async processSingleInvestmentEarning(investment, earningDate) {
    const startDate = new Date(investment.start_date);
    const endDate = new Date(investment.end_date);
    const currentDate = new Date(earningDate);

    // Check if investment is still active and within earning period
    if (currentDate < startDate || currentDate > endDate) {
      return null;
    }

    // Check if earnings already processed for this date
    const { data: existingEarning } = await supabase
      .from('daily_earnings')
      .select('id')
      .eq('investment_id', investment.id)
      .eq('earning_date', earningDate)
      .single();

    if (existingEarning) {
      console.log(`Earnings already processed for investment ${investment.id} on ${earningDate}`);
      return existingEarning;
    }

    // Calculate daily earning amount
    const dailyReturnRate = parseFloat(investment.investment_plans.daily_return) / 100;
    const investmentAmount = parseFloat(investment.amount);
    const dailyEarning = investmentAmount * dailyReturnRate;

    // Create daily earning record
    const { data: earningRecord, error: earningError } = await supabase
      .from('daily_earnings')
      .insert({
        user_id: investment.user_id,
        investment_id: investment.id,
        amount: dailyEarning,
        earning_date: earningDate
      })
      .select()
      .single();

    if (earningError) throw earningError;

    // Update user balance
    const currentBalance = parseFloat(investment.users.balance || 0);
    const newBalance = currentBalance + dailyEarning;

    const { error: balanceError } = await supabase
      .from('users')
      .update({ balance: newBalance })
      .eq('auth_id', investment.users.auth_id);

    if (balanceError) throw balanceError;

    // Update investment total return
    const currentTotalReturn = parseFloat(investment.total_return || 0);
    const newTotalReturn = currentTotalReturn + dailyEarning;

    const { error: investmentError } = await supabase
      .from('investments')
      .update({ total_return: newTotalReturn })
      .eq('id', investment.id);

    if (investmentError) throw investmentError;

    // Create transaction record for the earning
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: investment.users.auth_id,
        type: 'earning',
        amount: dailyEarning,
        status: 'completed',
        description: `Daily earning from ${investment.investment_plans.name} investment`
      });

    if (transactionError) throw transactionError;

    // Check if investment has completed its duration
    const daysElapsed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    if (daysElapsed >= investment.investment_plans.duration_days) {
      await this.completeInvestment(investment);
    }

    console.log(`Processed earning: $${dailyEarning} for investment ${investment.id}`);
    return earningRecord;
  }

  // Complete an investment when it reaches maturity
  async completeInvestment(investment) {
    try {
      // Get current user balance
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('balance')
        .eq('auth_id', investment.users.auth_id)
        .single();

      if (userError) throw userError;

      const currentBalance = parseFloat(userData.balance || 0);
      const principalAmount = parseFloat(investment.amount);
      const newBalance = currentBalance + principalAmount;

      // Return the principal amount to user's balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({ balance: newBalance })
        .eq('auth_id', investment.users.auth_id);

      if (balanceError) throw balanceError;

      // Update investment status to completed
      const { error: statusError } = await supabase
        .from('investments')
        .update({ status: 'completed' })
        .eq('id', investment.id);

      if (statusError) throw statusError;

      // Create transaction record for principal return
      const { error: principalTransactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: investment.users.auth_id,
          type: 'investment_return',
          amount: principalAmount,
          status: 'completed',
          description: `Principal return from ${investment.investment_plans.name} investment`
        });

      if (principalTransactionError) throw principalTransactionError;

      // Create completion transaction for tracking
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: investment.users.auth_id,
          type: 'investment_completed',
          amount: parseFloat(investment.total_return || 0),
          status: 'completed',
          description: `${investment.investment_plans.name} investment completed - Total earnings: $${parseFloat(investment.total_return || 0).toFixed(2)}`
        });

      if (transactionError) throw transactionError;

      console.log(`Investment ${investment.id} completed successfully. Principal $${principalAmount} returned to user balance.`);
    } catch (error) {
      console.error(`Error completing investment ${investment.id}:`, error);
      throw error;
    }
  }

  // Get user's total earnings
  async getUserTotalEarnings(userId) {
    try {
      const { data, error } = await supabase
        .from('daily_earnings')
        .select('amount')
        .eq('user_id', userId);

      if (error) throw error;

      const totalEarnings = data.reduce((sum, earning) => sum + parseFloat(earning.amount), 0);
      return totalEarnings;
    } catch (error) {
      console.error('Error getting user total earnings:', error);
      return 0;
    }
  }

  // Get user's earnings for a specific period
  async getUserEarningsForPeriod(userId, startDate, endDate) {
    try {
      let query = supabase
        .from('daily_earnings')
        .select('*')
        .eq('user_id', userId)
        .order('earning_date', { ascending: false });

      if (startDate) {
        query = query.gte('earning_date', startDate);
      }
      if (endDate) {
        query = query.lte('earning_date', endDate);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting user earnings for period:', error);
      return [];
    }
  }

  // Get active investments summary for a user
  async getUserActiveInvestments(userId) {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          investment_plans (
            name,
            daily_return,
            total_return,
            duration_days
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Calculate additional metrics for each investment
      const investmentsWithMetrics = data.map(investment => {
        const startDate = new Date(investment.start_date);
        const endDate = new Date(investment.end_date);
        const today = new Date();
        
        const daysElapsed = Math.max(0, Math.floor((today - startDate) / (1000 * 60 * 60 * 24)));
        const totalDays = investment.investment_plans.duration_days;
        const remainingDays = Math.max(0, totalDays - daysElapsed);
        const progressPercentage = Math.min(100, (daysElapsed / totalDays) * 100);

        const expectedTotalReturn = parseFloat(investment.amount) * (parseFloat(investment.investment_plans.total_return) / 100);
        const currentReturn = parseFloat(investment.total_return || 0);
        const remainingReturn = Math.max(0, expectedTotalReturn - currentReturn);

        return {
          ...investment,
          daysElapsed,
          remainingDays,
          progressPercentage: Math.round(progressPercentage * 100) / 100,
          expectedTotalReturn,
          currentReturn,
          remainingReturn
        };
      });

      return investmentsWithMetrics;
    } catch (error) {
      console.error('Error getting user active investments:', error);
      return [];
    }
  }

  // Initialize automatic daily processing (call this when app starts)
  initializeAutomaticProcessing() {
    // Process earnings immediately on startup
    setTimeout(() => {
      this.processAllDailyEarnings().catch(console.error);
    }, 5000);

    // Set up daily processing at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 1, 0, 0); // 12:01 AM

    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
      this.processAllDailyEarnings().catch(console.error);
      
      // Set up recurring daily processing
      setInterval(() => {
        this.processAllDailyEarnings().catch(console.error);
      }, 24 * 60 * 60 * 1000); // Every 24 hours
    }, msUntilMidnight);

    console.log('Automatic daily earnings processing initialized');
  }
}

export default new InvestmentEarningsService();
