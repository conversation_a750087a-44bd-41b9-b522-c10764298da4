-- BullSeed Referral System Database Tables
-- Run these SQL commands in your Supabase SQL editor

-- 1. Update users table to add referral fields (if not already present)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20) UNIQUE,
ADD COLUMN IF NOT EXISTS referred_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS referral_funds DECIMAL(10,2) DEFAULT 0;

-- Create index for referral code lookups
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
CREATE INDEX IF NOT EXISTS idx_users_referred_by ON users(referred_by);

-- 2. Create referrals table to track referral relationships
CREATE TABLE IF NOT EXISTS referrals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    referrer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    referred_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    referral_code VA<PERSON>HAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure a user can only be referred once
    UNIQUE(referred_id),
    -- Ensure referrer can't refer themselves
    CHECK (referrer_id != referred_id)
);

-- Create indexes for referrals table
CREATE INDEX IF NOT EXISTS idx_referrals_referrer_id ON referrals(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referrals_referred_id ON referrals(referred_id);
CREATE INDEX IF NOT EXISTS idx_referrals_code ON referrals(referral_code);

-- 3. Create referral_earnings table to track commissions
CREATE TABLE IF NOT EXISTS referral_earnings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    referrer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    referred_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    investment_amount DECIMAL(10,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL, -- e.g., 0.025 for 2.5%
    referral_level INTEGER DEFAULT 1, -- 1, 2, or 3 for multi-level
    investment_type VARCHAR(20) DEFAULT 'deposit' CHECK (investment_type IN ('deposit', 'investment', 'multi_level')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Ensure positive amounts
    CHECK (investment_amount > 0),
    CHECK (commission_amount >= 0),
    CHECK (commission_rate >= 0 AND commission_rate <= 1)
);

-- Create indexes for referral_earnings table
CREATE INDEX IF NOT EXISTS idx_referral_earnings_referrer_id ON referral_earnings(referrer_id);
CREATE INDEX IF NOT EXISTS idx_referral_earnings_referred_id ON referral_earnings(referred_id);
CREATE INDEX IF NOT EXISTS idx_referral_earnings_status ON referral_earnings(status);
CREATE INDEX IF NOT EXISTS idx_referral_earnings_created_at ON referral_earnings(created_at);

-- 4. Create trigger to update processed_at when status changes to completed
CREATE OR REPLACE FUNCTION update_referral_earnings_processed_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.processed_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_referral_earnings_processed_at
    BEFORE UPDATE ON referral_earnings
    FOR EACH ROW
    EXECUTE FUNCTION update_referral_earnings_processed_at();

-- 5. Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_referrals_updated_at
    BEFORE UPDATE ON referrals
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 6. Create function to generate unique referral codes
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS TEXT AS $$
DECLARE
    code TEXT;
    exists BOOLEAN;
BEGIN
    LOOP
        -- Generate code: BS + 8 random uppercase alphanumeric characters
        code := 'BS' || upper(substring(md5(random()::text) from 1 for 8));
        
        -- Check if code already exists
        SELECT EXISTS(SELECT 1 FROM users WHERE referral_code = code) INTO exists;
        
        -- If code doesn't exist, return it
        IF NOT exists THEN
            RETURN code;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger to auto-generate referral codes for new users
CREATE OR REPLACE FUNCTION auto_generate_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code = generate_referral_code();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_generate_referral_code
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_referral_code();

-- 8. Create view for referral statistics
CREATE OR REPLACE VIEW referral_stats AS
SELECT 
    u.id as user_id,
    u.auth_id,
    u.referral_code,
    u.referral_funds,
    COUNT(r.id) as total_referrals,
    COALESCE(SUM(re.commission_amount), 0) as total_earnings,
    COUNT(CASE WHEN r.created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as referrals_last_30_days,
    COALESCE(SUM(CASE WHEN re.created_at >= NOW() - INTERVAL '30 days' AND re.status = 'completed' THEN re.commission_amount ELSE 0 END), 0) as earnings_last_30_days
FROM users u
LEFT JOIN referrals r ON u.auth_id = r.referrer_id
LEFT JOIN referral_earnings re ON u.auth_id = re.referrer_id AND re.status = 'completed'
GROUP BY u.id, u.auth_id, u.referral_code, u.referral_funds;

-- 9. Enable Row Level Security (RLS) for security
ALTER TABLE referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_earnings ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies
-- Users can only see their own referral data
CREATE POLICY "Users can view their own referrals" ON referrals
    FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

CREATE POLICY "Users can view their own earnings" ON referral_earnings
    FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

-- Only the system can insert/update referral data (via service role)
CREATE POLICY "Service role can manage referrals" ON referrals
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage earnings" ON referral_earnings
    FOR ALL USING (auth.role() = 'service_role');

-- 11. Grant necessary permissions
GRANT SELECT ON referral_stats TO authenticated;
GRANT SELECT ON referrals TO authenticated;
GRANT SELECT ON referral_earnings TO authenticated;

-- 12. Create function to get referral link
CREATE OR REPLACE FUNCTION get_referral_link(user_referral_code TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN 'https://www.bullseed.online/registration?ref=' || user_referral_code;
END;
$$ LANGUAGE plpgsql;

-- Success message
SELECT 'BullSeed referral system tables created successfully!' as message;
