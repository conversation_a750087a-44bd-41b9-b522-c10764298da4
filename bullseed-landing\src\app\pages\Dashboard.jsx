import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TradingChart from '../components/TradingChart';
import BalanceCard from '../components/BalanceCard';
import ReferralCard from '../components/ReferralCard';
import MarketTicker from '../components/MarketTicker';
import { useUserPreferences } from '../../contexts/UserPreferencesContext';
import cryptoService from '../../services/cryptoService';
import reportService from '../../services/reportService';
import portfolioService from '../../services/portfolioService';
import portfolioPerformanceService from '../../services/portfolioPerformanceService';
import investmentEarningsService from '../../services/investmentEarningsService';
import dbService from '../../services/dbService';
import { supabase } from '../../lib/supabase';
import '../styles/Dashboard.css';

const Dashboard = ({ user }) => {
  const navigate = useNavigate();
  const { t, formatCurrency, formatCryptoPrice, formatPercentage, formatDisplayDate } = useUserPreferences();
  console.log('Dashboard - Rendering with user:', user);

  // Initialize all hooks first
  const [marketData, setMarketData] = useState([]);
  const [allCoins, setAllCoins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState([]);
  const [allTransactions, setAllTransactions] = useState([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [downloadingReport, setDownloadingReport] = useState(false);
  const [userStats, setUserStats] = useState({
    spentThisMonth: 0,
    change: 0,
    volume: 0,
    marketCap: 0,
    avgMonthlyGrowth: 0,
    totalInvested: 0,
    totalEarned: 0,
    portfolioGrowthPercentage: 15 // Default for new users
  });
  const [referralData, setReferralData] = useState({
    totalJoined: 0,
    referralEarn: 0,
    referralLink: ''
  });
  const [investments, setInvestments] = useState([]);
  const [activeInvestments, setActiveInvestments] = useState([]);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [userBalance, setUserBalance] = useState({
    balance: 0,
    earned_funds: 0,
    referral_funds: 0
  });
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const [portfolioPerformance, setPortfolioPerformance] = useState({
    performancePercentage: 15,
    totalGainLoss: 0,
    isPositive: true,
    insights: [],
    isDemo: true,
    demoAmount: 1000
  });
  const [dateRange, setDateRange] = useState('Last 30 days');
  const [selectedDateRange, setSelectedDateRange] = useState(30); // Days to filter data
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showAddCoinModal, setShowAddCoinModal] = useState(false);
  const [chartPanelMinimized, setChartPanelMinimized] = useState(true);
  const [activeTimeframe, setActiveTimeframe] = useState('1D');
  const [selectedChartCoin, setSelectedChartCoin] = useState('bitcoin'); // Default to Bitcoin
  const [portfolioCoins, setPortfolioCoins] = useState(['bitcoin', 'ethereum', 'cardano', 'solana', 'chainlink']);
  const [currentUser, setCurrentUser] = useState(null);
  const [portfolioLoading, setPortfolioLoading] = useState(false);

  // Get current user and load portfolio
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);

      if (user) {
        // Load user's portfolio using auth user ID
        const userPortfolio = await portfolioService.getPortfolioCoins(user.id);
        setPortfolioCoins(userPortfolio);
      }
    };

    getCurrentUser();

    // Initialize automatic investment earnings processing
    investmentEarningsService.initializeAutomaticProcessing();
  }, []);

  // Fetch real market data
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const data = await cryptoService.getMarketData();
        setMarketData(data);

        // Also fetch all available coins for the modal (get more coins)
        const allCoinsData = await cryptoService.getAllCoins(100);
        setAllCoins(allCoinsData);
      } catch (error) {
        console.error('Error fetching market data:', error);
        // Use fallback data if API fails
        setMarketData(cryptoService.getFallbackMarketData());
        setAllCoins(cryptoService.getFallbackAllCoins());
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();

    // Set up real-time updates with faster intervals
    const cleanup = cryptoService.startRealTimeUpdates(async (data) => {
      setMarketData(data);
      setLastUpdateTime(new Date());

      // Update portfolio performance with new market data
      await updatePortfolioPerformance();

      // Add a subtle flash effect to show data updated
      const tickerBar = document.querySelector('.market-ticker-bar');
      if (tickerBar) {
        tickerBar.classList.add('data-updated');
        setTimeout(() => tickerBar.classList.remove('data-updated'), 500);
      }
      // Also animate portfolio crypto items
      const cryptoItems = document.querySelectorAll('.portfolio-crypto-item');
      cryptoItems.forEach(item => {
        item.classList.add('data-updated');
        setTimeout(() => item.classList.remove('data-updated'), 300);
      });
    }, 5000); // Update every 5 seconds for real-time feel

    return cleanup;
  }, []);

  // DISABLED: This was causing race conditions with stats calculation
  // Portfolio performance will be updated manually after all data is loaded
  /*
  useEffect(() => {
    if (portfolioCoins.length > 0) {
      updatePortfolioPerformance();
    }
  }, [portfolioCoins, investments, userBalance]);
  */

  // DISABLED: This useEffect was causing race conditions
  // We'll handle stats calculation manually in the data loading functions
  /*
  useEffect(() => {
    console.log('=== useEffect triggered for totalEarnings change ===');
    console.log('totalEarnings:', totalEarnings);
    console.log('transactions.length:', transactions.length);
    console.log('allTransactions.length:', allTransactions.length);
    console.log('investments.length:', investments.length);

    if ((transactions.length > 0 || allTransactions.length > 0) && investments.length > 0) {
      // Use allTransactions if available, otherwise fall back to transactions
      const transactionsToUse = allTransactions.length > 0 ? allTransactions : transactions;
      console.log('Using transactions count:', transactionsToUse.length);
      calculateUserStats(transactionsToUse, investments, totalEarnings);
    }
  }, [totalEarnings, transactions, allTransactions, investments]);
  */

  // Helper function to filter data based on date range
  const filterDataByDateRange = (data, days) => {
    if (!data || !Array.isArray(data)) return data;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return data.filter(item => {
      if (item.date) {
        const itemDate = new Date(item.date);
        return itemDate >= cutoffDate;
      }
      return true; // Keep items without dates
    });
  };

  // Helper function to handle date range selection
  const handleDateRangeChange = (range, days) => {
    setDateRange(range);
    setSelectedDateRange(days);
    setShowDatePicker(false);

    // Update chart timeframe based on selected range
    if (days <= 7) {
      setActiveTimeframe('1D');
    } else if (days <= 30) {
      setActiveTimeframe('5D');
    } else if (days <= 90) {
      setActiveTimeframe('1M');
    } else if (days <= 180) {
      setActiveTimeframe('3M');
    } else if (days <= 365) {
      setActiveTimeframe('6M');
    } else {
      setActiveTimeframe('1Y');
    }

    // Update portfolio performance based on new date range
    updatePortfolioPerformance();

    console.log(`Updated dashboard view for ${range} (${days} days)`);
  };

  // Handle adding coin to portfolio
  const handleAddCoin = async (coinId) => {
    if (!currentUser) {
      // For demo purposes, allow local changes without login
      if (portfolioCoins.length >= 5) {
        alert('Portfolio is full (max 5 coins)');
        return;
      }
      if (portfolioCoins.includes(coinId)) {
        alert('Coin already in portfolio');
        return;
      }
      setPortfolioCoins([...portfolioCoins, coinId]);
      return;
    }

    setPortfolioLoading(true);
    try {
      const result = await portfolioService.addCoinToPortfolio(currentUser.id, coinId);

      if (result.success) {
        setPortfolioCoins(result.coins);
        console.log(result.message);
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('Error adding coin:', error);
      // Fallback to local state
      if (portfolioCoins.length < 5 && !portfolioCoins.includes(coinId)) {
        setPortfolioCoins([...portfolioCoins, coinId]);
      }
    } finally {
      setPortfolioLoading(false);
    }
  };

  // Handle removing coin from portfolio
  const handleRemoveCoin = async (coinId) => {
    if (!currentUser) {
      // For demo purposes, allow local changes without login
      setPortfolioCoins(portfolioCoins.filter(id => id !== coinId));
      return;
    }

    setPortfolioLoading(true);
    try {
      const result = await portfolioService.removeCoinFromPortfolio(currentUser.id, coinId);

      if (result.success) {
        setPortfolioCoins(result.coins);
        console.log(result.message);
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('Error removing coin:', error);
      // Fallback to local state
      setPortfolioCoins(portfolioCoins.filter(id => id !== coinId));
    } finally {
      setPortfolioLoading(false);
    }
  };

  // Function to refresh all dashboard data
  const refreshDashboardData = async () => {
    if (!user?.id) {
      console.log('No user ID available');
      setTransactionsLoading(false);
      return;
    }

    // Determine the correct auth ID to use for database queries
    const authId = user.auth_id || user.id;
    console.log('Dashboard - Refreshing data for auth ID:', authId);

    try {
      setTransactionsLoading(true);

      // Update user balance from user prop
      setUserBalance({
        balance: user.balance || 0,
        earned_funds: user.earned_funds || 0,
        referral_funds: user.referral_funds || 0
      });

      // Fetch fresh data from database
      const userData = await dbService.getUser(authId);
      if (userData) {
        setUserBalance({
          balance: parseFloat(userData.balance || 0),
          earned_funds: parseFloat(userData.earned_funds || 0),
          referral_funds: parseFloat(userData.referral_funds || 0)
        });
      }

      // Get recent transactions for display (limit 10)
      const userTransactions = await dbService.getTransactions(authId, 10);
      if (userTransactions) {
        console.log('Dashboard - Refreshed transactions:', userTransactions);
        setTransactions(userTransactions);
      }

      // Get ALL transactions for accurate calculations
      const allTransactions = await dbService.getTransactions(authId, 1000);
      if (allTransactions) {
        setAllTransactions(allTransactions);
      }

      const userInvestments = await dbService.getInvestments(authId);
      if (userInvestments) {
        console.log('Dashboard - Refreshed investments:', userInvestments);
        setInvestments(userInvestments);
      }

      // Load fresh total earnings
      const userTotalEarnings = await investmentEarningsService.getUserTotalEarnings(authId);
      setTotalEarnings(userTotalEarnings);
      console.log('Dashboard - Refreshed total earnings:', userTotalEarnings);

      const referrals = await dbService.getReferralData(authId);
      if (referrals) {
        setReferralData({
          totalJoined: referrals.totalJoined,
          referralEarn: referrals.referralEarn,
          referralLink: `https://www.bullseed.online/registration?ref=${userData?.referral_code || user?.referral_code || 'default'}`
        });
      }

      // Calculate stats with fresh data - use allTransactions for accurate deposit calculation
      const transactionsForStats = allTransactions.length > 0 ? allTransactions : (userTransactions || []);
      console.log('=== refreshData calculating stats ===');
      console.log('Using transactions count:', transactionsForStats.length);
      calculateUserStats(transactionsForStats, userInvestments || [], userTotalEarnings);

      // Update portfolio performance after stats are calculated
      if (portfolioCoins.length > 0) {
        setTimeout(() => updatePortfolioPerformance(), 100);
      }

      setTransactionsLoading(false);
    } catch (error) {
      console.error('Dashboard - Error refreshing data:', error);
      setTransactionsLoading(false);
    }
  };

  // Fetch all user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user?.id) {
        console.log('No user ID available');
        setTransactionsLoading(false);
        return;
      }

      // Determine the correct auth ID to use for database queries
      const authId = user.auth_id || user.id;
      console.log('Dashboard - User object:', user);
      console.log('Dashboard - Using auth ID for queries:', authId);

      try {
        setTransactionsLoading(true);

        // Set default data first
        setTransactions([]);
        setInvestments([]);
        setReferralData({
          totalJoined: 0,
          referralEarn: 0,
          referralLink: `https://www.bullseed.com/registration?ref=${user?.referral_code || 'default'}`
        });
        setUserBalance({
          balance: user.balance || 0,
          earned_funds: user.earned_funds || 0,
          referral_funds: user.referral_funds || 0
        });

        // Don't calculate stats with empty data - wait for real data to load
        console.log('=== Skipping calculateUserStats with empty data ===');

        // Try to fetch additional data from database (optional)
        try {
          const userData = await dbService.getUser(authId);
          if (userData) {
            setUserBalance({
              balance: parseFloat(userData.balance || 0),
              earned_funds: parseFloat(userData.earned_funds || 0),
              referral_funds: parseFloat(userData.referral_funds || 0)
            });
          }

          // Get recent transactions for display (limit 10)
          const userTransactions = await dbService.getTransactions(authId, 10);
          if (userTransactions) {
            console.log('Dashboard - Loaded transactions:', userTransactions);
            setTransactions(userTransactions);
          }

          // Get ALL transactions for accurate calculations
          const allTransactions = await dbService.getTransactions(authId, 1000);
          if (allTransactions) {
            setAllTransactions(allTransactions);
          }

          const userInvestments = await dbService.getInvestments(authId);
          if (userInvestments) {
            console.log('Dashboard - Loaded investments:', userInvestments);
            setInvestments(userInvestments);
          }

          // Load active investments with detailed metrics
          const activeInvestmentsData = await investmentEarningsService.getUserActiveInvestments(authId);
          if (activeInvestmentsData) {
            console.log('Dashboard - Loaded active investments:', activeInvestmentsData);
            setActiveInvestments(activeInvestmentsData);
          }

          // Load total earnings
          const userTotalEarnings = await investmentEarningsService.getUserTotalEarnings(authId);
          setTotalEarnings(userTotalEarnings);
          console.log('Dashboard - Total earnings:', userTotalEarnings);

          const referrals = await dbService.getReferralData(authId);
          if (referrals) {
            setReferralData({
              totalJoined: referrals.totalJoined,
              referralEarn: referrals.referralEarn,
              referralLink: `https://www.bullseed.online/registration?ref=${userData?.referral_code || user?.referral_code || 'default'}`
            });
          }

          // Calculate stats with real data - use allTransactions for accurate deposit calculation
          const transactionsForStats = allTransactions.length > 0 ? allTransactions : (userTransactions || []);
          console.log('=== fetchUserData calculating final stats ===');
          console.log('Using transactions count:', transactionsForStats.length);
          console.log('Investments count:', (userInvestments || []).length);
          calculateUserStats(transactionsForStats, userInvestments || [], userTotalEarnings);

          // Update portfolio performance after stats are calculated
          if (portfolioCoins.length > 0) {
            setTimeout(() => updatePortfolioPerformance(), 200);
          }
        } catch (dbError) {
          console.log('Dashboard - Database queries failed, using default data:', dbError);
        }

      } catch (error) {
        console.error('Error fetching user data:', error);
        // Keep the default data we already set
      } finally {
        setTransactionsLoading(false);
      }
    };

    fetchUserData();

    // Listen for balance update events from other components
    const handleBalanceUpdate = () => {
      console.log('Dashboard - Received balance update event, refreshing data');
      refreshDashboardData();
    };

    window.addEventListener('userBalanceUpdated', handleBalanceUpdate);

    // Set up real-time subscription for user data changes
    let userSubscription = null;
    if (user?.id) {
      const authId = user.auth_id || user.id;

      userSubscription = supabase
        .channel(`user_data_changes_${authId}`)
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'users',
            filter: `auth_id=eq.${authId}`
          },
          (payload) => {
            console.log('Dashboard - User data updated:', payload);
            // Refresh user data when balance changes
            setTimeout(() => fetchUserData(), 100); // Small delay to ensure DB consistency
          }
        )
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${authId}`
          },
          (payload) => {
            console.log('Dashboard - New transaction:', payload);
            // Refresh user data when new transaction is added
            setTimeout(() => fetchUserData(), 100);
          }
        )
        .subscribe((status, err) => {
          console.log('Dashboard subscription status:', status);
          if (err) {
            console.error('Dashboard subscription error:', err);
          }
          if (status === 'SUBSCRIBED') {
            console.log('✅ Dashboard real-time subscription is active');
          }
        });
    }

    return () => {
      if (userSubscription) {
        userSubscription.unsubscribe();
      }
      window.removeEventListener('userBalanceUpdated', handleBalanceUpdate);
    };
  }, [user]);

  // Calculate user statistics from real data
  const calculateUserStats = (transactions, investments, currentTotalEarnings = null) => {
    console.log('=== calculateUserStats called ===');
    console.log('Transactions count:', transactions.length);
    console.log('Investments count:', investments.length);
    console.log('Current total earnings:', currentTotalEarnings);
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // Calculate INVESTMENTS made this month (not deposits)
    const thisMonthInvestments = investments.filter(inv => {
      const investmentDate = new Date(inv.created_at);
      return investmentDate.getMonth() === currentMonth &&
             investmentDate.getFullYear() === currentYear;
    });

    const spentThisMonth = thisMonthInvestments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);

    // Calculate total deposits (for VOLUME display) - use ALL transactions, not just recent ones
    const transactionsToUse = allTransactions.length > 0 ? allTransactions : transactions;
    const depositTransactions = transactionsToUse.filter(t => t.type === 'deposit' && t.status === 'completed');
    console.log('Dashboard - All transactions for calculation:', transactionsToUse.length);
    console.log('Dashboard - Deposit transactions:', depositTransactions);
    const totalDeposits = depositTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
    console.log('Dashboard - Total deposits calculated:', totalDeposits);

    // Calculate total investments
    const totalInvestments = investments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);

    // Use real total earnings from parameter or state (for MARKET CAP display)
    const realTotalEarnings = currentTotalEarnings !== null ? currentTotalEarnings : (totalEarnings || 0);

    // Calculate portfolio growth percentage
    let portfolioGrowthPercentage = 15; // Default for new users
    if (totalInvestments > 0) {
      // Calculate actual growth percentage based on real earnings vs investments
      portfolioGrowthPercentage = Math.min(Math.max(((realTotalEarnings / totalInvestments) * 100), 0), 100);
    } else if (spentThisMonth > 0) {
      // For users who have made deposits but no investments yet, show progress based on deposits
      portfolioGrowthPercentage = Math.min((spentThisMonth / 1000) * 10, 25); // Up to 25% for $250+ deposits
    }

    // Calculate actual average monthly growth percentage
    let avgMonthlyGrowth = 0;
    if (totalInvestments > 0 && investments.length > 0) {
      // Find the oldest investment to calculate time period
      const oldestInvestment = investments.reduce((oldest, inv) => {
        const invDate = new Date(inv.created_at);
        const oldestDate = new Date(oldest.created_at);
        return invDate < oldestDate ? inv : oldest;
      });

      const monthsSinceFirstInvestment = Math.max(1,
        (now - new Date(oldestInvestment.created_at)) / (1000 * 60 * 60 * 24 * 30)
      );

      // Calculate monthly growth rate
      avgMonthlyGrowth = (realTotalEarnings / totalInvestments / monthsSinceFirstInvestment) * 100;
    }

    // Calculate change percentage (real earnings vs investments)
    const changePercentage = totalInvestments > 0 ? ((realTotalEarnings / totalInvestments) * 100) : 0;

    const newStats = {
      spentThisMonth: spentThisMonth, // Amount invested this month
      change: changePercentage,
      volume: totalDeposits, // Total amount deposited
      marketCap: realTotalEarnings, // Total earnings
      avgMonthlyGrowth: avgMonthlyGrowth, // Actual monthly growth percentage
      totalInvested: totalInvestments,
      totalEarned: realTotalEarnings,
      portfolioGrowthPercentage: portfolioGrowthPercentage
    };

    console.log('=== Setting new user stats ===');
    console.log('Volume (Amount Deposited):', totalDeposits);
    console.log('Portfolio Growth Percentage:', portfolioGrowthPercentage);
    console.log('Full stats object:', newStats);

    setUserStats(newStats);
  };

  // Update portfolio performance with real-time data
  const updatePortfolioPerformance = async () => {
    try {
      const authId = user?.auth_id || user?.id;
      console.log('Dashboard - Portfolio performance using authId:', authId, 'investments:', investments.length);

      const performance = await portfolioPerformanceService.getPortfolioPerformance(
        authId,
        portfolioCoins,
        investments,
        userBalance.balance
      );

      const insights = portfolioPerformanceService.getPortfolioInsights(performance, portfolioCoins);

      setPortfolioPerformance({
        performancePercentage: performance.performancePercentage,
        totalGainLoss: performance.totalGainLoss,
        isPositive: performance.isPositive,
        insights: insights,
        isDemo: performance.isDemo,
        demoAmount: performance.demoAmount
      });

      // CAREFULLY update only the portfolio percentage without touching other stats
      console.log('=== updatePortfolioPerformance updating ONLY portfolio percentage ===');
      console.log('Previous userStats volume:', userStats.volume);
      console.log('New performance percentage:', performance.performancePercentage);

      // Only update if we have existing stats and the volume is not zero (to avoid overwriting during loading)
      if (userStats.volume > 0) {
        setUserStats(prev => {
          const updated = {
            ...prev,
            portfolioGrowthPercentage: performance.performancePercentage
          };
          console.log('Updated userStats - volume preserved:', updated.volume);
          return updated;
        });
      } else {
        console.log('Skipping portfolio update - userStats not fully loaded yet');
      }
    } catch (error) {
      console.error('Error updating portfolio performance:', error);
    }
  };

  // Handle report download
  const handleDownloadReport = async () => {
    try {
      setDownloadingReport(true);
      console.log('Starting report generation...', { user, transactions: transactions.length, marketData: marketData.length });

      // Create enhanced user object with current earned funds and referral funds
      const userWithEarnings = {
        ...user,
        earned_funds: totalEarnings || 0,
        earnedFunds: totalEarnings || 0,
        referral_funds: referralData.referralEarn || 0,
        referralFunds: referralData.referralEarn || 0
      };

      console.log('Report user data:', {
        name: userWithEarnings.name,
        balance: userWithEarnings.balance,
        earned_funds: userWithEarnings.earned_funds,
        referral_funds: userWithEarnings.referral_funds
      });

      const result = await reportService.downloadReport(userWithEarnings, transactions, investments, marketData);

      if (result.success) {
        console.log(`✅ Report downloaded successfully: ${result.fileName}`);
        // You could add a toast notification here for better UX
      } else {
        console.error('❌ Failed to generate report:', result.error);
        alert(`Failed to generate report: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Error downloading report:', error);
      alert(`An error occurred while generating the report: ${error.message}`);
    } finally {
      setDownloadingReport(false);
    }
  };

  // Show loading state if user data is not yet available
  if (!user) {
    return (
      <div className="dashboard">
        <div className="dashboard-loading">
          <div className="loading-spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      {/* Market Ticker */}
      <div className="market-ticker-bar">
        <div className="ticker-container">
          <div className="ticker-live-indicator">
            <span className="live-dot"></span>
            <span className="live-text">LIVE</span>
          </div>
          <div className="ticker-scroll">
            {marketData.map((coin, index) => (
              <div key={index} className="ticker-item">
                <span className="ticker-symbol">{coin.symbol.split('/')[0]}</span>
                <span className="ticker-price">{formatCryptoPrice(coin.price || 0)}</span>
                <span className={`ticker-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {coin.change >= 0 ? '▲' : '▼'} {Math.abs(coin.change || 0).toFixed(2)}%
                </span>
              </div>
            ))}
            {/* Duplicate for seamless scroll */}
            {marketData.map((coin, index) => (
              <div key={`dup-${index}`} className="ticker-item">
                <span className="ticker-symbol">{coin.symbol.split('/')[0]}</span>
                <span className="ticker-price">{formatCryptoPrice(coin.price || 0)}</span>
                <span className={`ticker-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {coin.change >= 0 ? '▲' : '▼'} {Math.abs(coin.change || 0).toFixed(2)}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>Welcome back, {user.name ? user.name.split(' ')[0] : 'User'}</h1>
          <p>Here's a look at your performance and analytics.</p>
        </div>
        <div className="dashboard-header-controls">
          <div className="date-selector" onClick={() => setShowDatePicker(!showDatePicker)}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
            {dateRange}
            {showDatePicker && (
              <div className="date-picker-dropdown">
                <div onClick={() => handleDateRangeChange('Last 7 days', 7)}>
                  Last 7 days
                </div>
                <div onClick={() => handleDateRangeChange('Last 30 days', 30)}>
                  Last 30 days
                </div>
                <div onClick={() => handleDateRangeChange('Last 3 months', 90)}>
                  Last 3 months
                </div>
                <div onClick={() => handleDateRangeChange('Last 6 months', 180)}>
                  Last 6 months
                </div>
                <div onClick={() => handleDateRangeChange('Last year', 365)}>
                  Last year
                </div>
                <div onClick={() => handleDateRangeChange('All time', 9999)}>
                  All time
                </div>
              </div>
            )}
          </div>
          <button className="add-new-btn" onClick={() => setShowAddCoinModal(true)}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="5" x2="12" y2="19"/>
              <line x1="5" y1="12" x2="19" y2="12"/>
            </svg>
            Add new coin
          </button>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Left Column - Stats */}
        <div className="dashboard-stats">
          <div className="stat-card">
            <div className="stat-label">{t('totalInvested').toUpperCase()}</div>
            <div className="stat-value">{formatCurrency(userStats.spentThisMonth)}</div>
            <div className={`stat-change ${userStats.change >= 0 ? 'positive' : 'negative'}`}>
              <span>{formatPercentage(userStats.change)}</span>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-label">{t('amount').toUpperCase()} DEPOSITED</div>
            <div className="stat-value">{formatCurrency(userStats.volume)}</div>
          </div>

          <div className="stat-card">
            <div className="stat-label">{t('totalEarned').toUpperCase()}</div>
            <div className="stat-value">{formatCurrency(userStats.marketCap)}</div>
          </div>

          <div className="stat-card">
            <div className="stat-label">AVG MONTHLY GROWTH</div>
            <div className="stat-value">{userStats.avgMonthlyGrowth.toFixed(2)}%</div>
          </div>

          <button
            className="download-report-btn"
            onClick={handleDownloadReport}
            disabled={downloadingReport}
          >
            {downloadingReport ? (
              <>
                <div className="loading-spinner-small"></div>
                Generating...
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Download Report
              </>
            )}
          </button>
        </div>

        {/* Center Column - Chart */}
        <div className="dashboard-chart">
          <div className="trading-chart-header">
            <div className="trading-chart-title">Portfolio Overview</div>
            <div className="chart-coin-selector">
              <select
                value={selectedChartCoin}
                onChange={(e) => setSelectedChartCoin(e.target.value)}
                className="chart-coin-select"
              >
                {marketData.map((coin) => (
                  <option key={coin.id} value={coin.id}>
                    {coin.name} (${coin.price?.toLocaleString() || '0'})
                  </option>
                ))}
              </select>
            </div>
            <div className="trading-chart-info">
              {(() => {
                const selectedCoin = marketData.find(coin => coin.id === selectedChartCoin);
                return selectedCoin ? (
                  <div className="chart-price-item">
                    <div className="chart-price-dot" style={{backgroundColor: '#00d4aa'}}></div>
                    <span className="chart-symbol">{selectedCoin.symbol.split('/')[0]}</span>
                    <span className="price-current">${selectedCoin.price?.toLocaleString() || '0'}</span>
                    <span className={`price-change ${selectedCoin.change >= 0 ? 'positive' : 'negative'}`}>
                      {selectedCoin.change >= 0 ? '+' : ''}{selectedCoin.change?.toFixed(2) || '0.00'}%
                    </span>
                  </div>
                ) : null;
              })()}
            </div>
          </div>

          <div className="chart-container">
            <TradingChart timeframe={activeTimeframe} coinId={selectedChartCoin} />

            {/* Additional Chart Data Panel */}
            <div className={`chart-data-panel ${chartPanelMinimized ? 'minimized' : ''}`}>
              <div className="chart-panel-header">
                <button
                  className="chart-panel-toggle"
                  onClick={() => setChartPanelMinimized(!chartPanelMinimized)}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    {chartPanelMinimized ? (
                      <polyline points="18,15 12,9 6,15"/>
                    ) : (
                      <polyline points="6,9 12,15 18,9"/>
                    )}
                  </svg>
                </button>
              </div>

              {!chartPanelMinimized && (
                <>
                  <div className="chart-metrics">
                    {(() => {
                      const selectedCoin = marketData.find(coin => coin.id === selectedChartCoin);
                      return selectedCoin ? (
                        <>
                          <div className="metric-group">
                            <div className="metric-label">24h Volume</div>
                            <div className="metric-value">{selectedCoin.volume || 'N/A'}</div>
                          </div>
                          <div className="metric-group">
                            <div className="metric-label">Market Cap</div>
                            <div className="metric-value">${(selectedCoin.market_cap / 1e12).toFixed(2)}T</div>
                          </div>
                          <div className="metric-group">
                            <div className="metric-label">24h Change</div>
                            <div className={`metric-value ${selectedCoin.change >= 0 ? 'positive' : 'negative'}`}>
                              {selectedCoin.change >= 0 ? '+' : ''}{selectedCoin.change?.toFixed(2) || '0.00'}%
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="metric-group">
                            <div className="metric-label">24h Volume</div>
                            <div className="metric-value">Loading...</div>
                          </div>
                          <div className="metric-group">
                            <div className="metric-label">Market Cap</div>
                            <div className="metric-value">Loading...</div>
                          </div>
                          <div className="metric-group">
                            <div className="metric-label">24h Change</div>
                            <div className="metric-value">Loading...</div>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  <div className="chart-timeframes">
                    {['1D', '5D', '1M', '3M', '6M', 'YTD', '1Y', 'All'].map((timeframe) => (
                      <button
                        key={timeframe}
                        className={`timeframe-btn ${activeTimeframe === timeframe ? 'active' : ''}`}
                        onClick={() => setActiveTimeframe(timeframe)}
                      >
                        {timeframe}
                      </button>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Portfolio Performance */}
        <div className="dashboard-portfolio">
          <div className="portfolio-header">
            Portfolio Performance
            <div className="portfolio-header-info">
              <span className="info-icon">ℹ️</span>
              <div className="info-tooltip">
                Tracks your investment returns and portfolio growth
              </div>
            </div>
          </div>

          {/* Show portfolio performance only if user has investments */}
          {investments.length > 0 ? (
            <>
              <div
                className="portfolio-circle"
                style={{
                  background: `conic-gradient(from 0deg, #22c55e 0deg ${userStats.portfolioGrowthPercentage * 3.6}deg, #1f1f1f ${userStats.portfolioGrowthPercentage * 3.6}deg 360deg)`
                }}
              >
                <div className="portfolio-value">
                  {Math.round(userStats.portfolioGrowthPercentage)}%
                </div>
                <div className="portfolio-label">Growth</div>
              </div>
              <div className="portfolio-info">
                <div className="portfolio-date">Last updated: {lastUpdateTime.toLocaleDateString('en-US', { day: '2-digit', month: 'short' })} at {lastUpdateTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}</div>
                <div className="portfolio-status">
                  {(() => {
                    if (userStats.totalEarned > 0) {
                      return `Earned $${userStats.totalEarned.toFixed(2)} from investments`;
                    } else if (userStats.totalInvested > 0) {
                      return `$${userStats.totalInvested.toFixed(2)} actively invested`;
                    } else {
                      return 'Building your portfolio';
                    }
                  })()}
                </div>
              </div>
            </>
          ) : (
            <div className="no-investments-message">
              <div className="no-investments-icon">📊</div>
              <div className="no-investments-text">
                <h3>Start Your Investment Journey</h3>
                <p>Choose an investment plan to begin tracking your portfolio performance</p>
                <button
                  className="start-investing-btn"
                  onClick={() => navigate('/invest')}
                >
                  View Investment Plans
                </button>
              </div>
            </div>
          )}

          {/* Portfolio Insights */}
          {portfolioPerformance.insights.length > 0 && (
            <div className="portfolio-insights">
              {portfolioPerformance.insights.slice(0, 2).map((insight, index) => (
                <div key={index} className={`insight-item ${insight.type}`}>
                  <span className="insight-icon">{insight.icon}</span>
                  <span className="insight-message">{insight.message}</span>
                </div>
              ))}
            </div>
          )}

          {/* Multiple Crypto Tracking */}
          <div className="portfolio-cryptos">
            {portfolioCoins.map((coinId) => {
              const coin = marketData.find(c => c.id === coinId);
              if (!coin) return null;

              return (
                <div key={coinId} className="portfolio-crypto-item">
                  <div className="crypto-info">
                    <img src={coin.image} alt={coin.name} className="crypto-icon" />
                    <span className="crypto-label">{coin.name}</span>
                  </div>
                  <div className="crypto-data">
                    <div className="crypto-value">${coin.current_price?.toLocaleString() || '0'}</div>
                    <div className={`crypto-change ${(coin.price_change_percentage_24h || 0) >= 0 ? 'positive' : 'negative'}`}>
                      {(coin.price_change_percentage_24h || 0) >= 0 ? '+' : ''}
                      {coin.price_change_percentage_24h?.toFixed(2) || '0.00'}%
                    </div>
                  </div>
                </div>
              );
            })}

            {/* NFT Tracking */}
            <div className="portfolio-nft-section">
              <div className="nft-header">
                <span>🖼️ NFT Collections</span>
              </div>
              <div className="nft-item">
                <div className="nft-info">
                  <div className="nft-icon">🐵</div>
                  <span className="nft-label">BAYC</span>
                </div>
                <div className="nft-data">
                  <div className="nft-value">12.5 ETH</div>
                  <div className="nft-change positive">+5.2%</div>
                </div>
              </div>
              <div className="nft-item">
                <div className="nft-info">
                  <div className="nft-icon">💎</div>
                  <span className="nft-label">CryptoPunks</span>
                </div>
                <div className="nft-data">
                  <div className="nft-value">45.8 ETH</div>
                  <div className="nft-change negative">-2.1%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Active Investments */}
        {activeInvestments.length > 0 && (
          <div className="active-investments">
            <div className="section-header">
              <h3>Active Investments</h3>
              <button
                className="view-all-btn"
                onClick={() => navigate('/app/active-investments')}
              >
                View All
              </button>
            </div>
            <div className="active-investments-grid">
              {activeInvestments.map((investment) => (
                <div key={investment.id} className="active-investment-card">
                  <div className="investment-header">
                    <h4>{investment.investment_plans.name}</h4>
                    <div className="investment-status">
                      <span className="status-dot active"></span>
                      Active
                    </div>
                  </div>
                  <div className="investment-details">
                    <div className="investment-amount">
                      <span className="label">Invested:</span>
                      <span className="value">${parseFloat(investment.amount).toLocaleString()}</span>
                    </div>
                    <div className="investment-earned">
                      <span className="label">Earned:</span>
                      <span className="value earned">${investment.currentReturn.toFixed(2)}</span>
                    </div>
                    <div className="investment-progress">
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${investment.progressPercentage}%` }}
                        ></div>
                      </div>
                      <span className="progress-text">{investment.progressPercentage}% Complete</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Payment History */}
        <div className="payment-history">
          <h3>Payment History</h3>
          {transactionsLoading ? (
            <div className="payment-history-loading">
              <div className="loading-spinner"></div>
              <span>Loading transactions...</span>
            </div>
          ) : (
            <div className="payment-history-table-wrapper">
              <div className="payment-history-table">
                <div className="payment-history-header">
                  <div className="payment-col">NAME</div>
                  <div className="payment-col">DATE</div>
                  <div className="payment-col">PRICE</div>
                  <div className="payment-col">STATUS</div>
                </div>
              <div className="payment-history-body">
                {transactions.length === 0 ? (
                  <div className="payment-history-empty">
                    <p>No transactions found</p>
                  </div>
                ) : (
                  transactions.map((transaction) => (
                    <div key={transaction.id} className="payment-history-row">
                      <div className="payment-col">
                        <div className="payment-crypto">
                          <div className={`payment-crypto-icon ${transaction.crypto_currency?.toLowerCase() || 'default'}`}>
                            {transaction.crypto_currency?.charAt(0) || 'T'}
                          </div>
                          <span>{transaction.description || `${transaction.type} Transaction`}</span>
                        </div>
                      </div>
                      <div className="payment-col">
                        {new Date(transaction.created_at).toLocaleDateString('en-US', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </div>
                      <div className="payment-col">${transaction.amount.toLocaleString()}</div>
                      <div className="payment-col">
                        <span className={`payment-status ${
                          transaction.status === 'completed' ? 'success' :
                          transaction.status === 'pending' ? 'pending' : 'failed'
                        }`}>
                          {transaction.status === 'completed' ? 'Success' :
                           transaction.status === 'pending' ? 'Pending' : 'Failed'}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Coin Modal */}
      {showAddCoinModal && (
        <div className="modal-overlay" onClick={() => setShowAddCoinModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Manage Portfolio Coins</h3>
              <button className="modal-close" onClick={() => setShowAddCoinModal(false)}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"/>
                  <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
              </button>
            </div>
            <div className="modal-body">
              <div className="current-portfolio">
                <h4>Current Portfolio (Max 5 coins)</h4>
                <div className="current-coins">
                  {portfolioCoins.map((coinId, index) => {
                    const coin = allCoins.find(c => c.id === coinId) || marketData.find(c => c.id === coinId);
                    return (
                      <div key={coinId} className="current-coin-item">
                        <img
                          src={coin?.image || `https://assets.coingecko.com/coins/images/1/large/bitcoin.png`}
                          alt={coin?.name || coinId}
                          className="coin-icon"
                          onError={(e) => {
                            e.target.src = `https://via.placeholder.com/32/00d4aa/ffffff?text=${(coin?.symbol || coinId).charAt(0)}`;
                          }}
                        />
                        <span className="coin-name">{coin?.name || coinId}</span>
                        <button
                          className="remove-coin-btn"
                          onClick={() => handleRemoveCoin(coinId)}
                          disabled={portfolioLoading}
                        >
                          {portfolioLoading ? '...' : '×'}
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="available-coins">
                <h4>
                  Available Coins
                  {portfolioCoins.length >= 5 ? ' (Remove a coin first)' : ''}
                  {portfolioLoading && ' (Updating...)'}
                </h4>
                <div className="coin-selection-grid">
                  {allCoins
                    .filter(coin => !portfolioCoins.includes(coin.id))
                    .slice(0, 30)
                    .map((coin) => (
                      <div
                        key={coin.id}
                        className={`coin-option ${portfolioCoins.length >= 5 || portfolioLoading ? 'disabled' : ''}`}
                        onClick={() => {
                          if (portfolioCoins.length < 5 && !portfolioLoading) {
                            handleAddCoin(coin.id);
                          }
                        }}
                      >
                        <img
                          src={coin.image}
                          alt={coin.name}
                          className="coin-icon"
                          onError={(e) => {
                            e.target.src = `https://via.placeholder.com/32/00d4aa/ffffff?text=${coin.symbol.charAt(0)}`;
                          }}
                        />
                        <div className="coin-info">
                          <div className="coin-name">{coin.name}</div>
                          <div className="coin-symbol">{coin.symbol}</div>
                        </div>
                        <div className="coin-price">${coin.current_price?.toLocaleString()}</div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
